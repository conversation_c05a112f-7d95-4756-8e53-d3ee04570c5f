import {
  S3<PERSON>lient,
  PutObjectCommand,
  DeleteObjectCommand,
  GetO<PERSON>Command,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { Upload } from "@aws-sdk/lib-storage";
import { createId } from "@paralleldrive/cuid2";

interface S3Config {
  endpoint?: string;
  region: string;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
  publicUrl?: string;
}

interface UploadResult {
  key: string;
  url: string;
  size: number;
  contentType: string;
}

interface MediaUploadOptions {
  userId: string;
  category?: 'receipts' | 'documents' | 'images' | 'general';
  filename?: string;
  contentType?: string;
}

class S3Storage {
  private client: S3Client;
  private config: S3Config;

  constructor(config: S3Config) {
    this.config = config;
    
    const clientConfig: any = {
      region: config.region,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    };

    // Add endpoint for S3-compatible services (like MinIO, DigitalOcean Spaces, etc.)
    if (config.endpoint) {
      clientConfig.endpoint = config.endpoint;
      clientConfig.forcePathStyle = true; // Required for some S3-compatible services
    }

    this.client = new S3Client(clientConfig);
  }

  /**
   * Generate a unique file key with user isolation and categorization
   */
  private generateFileKey(options: MediaUploadOptions, originalFilename: string): string {
    const { userId, category = 'general', filename } = options;
    const fileId = createId();
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const extension = originalFilename.split('.').pop() || '';
    const finalFilename = filename || `${fileId}.${extension}`;
    
    return `media/${userId}/${category}/${timestamp}/${finalFilename}`;
  }

  /**
   * Upload a file to S3-compatible storage
   */
  async uploadFile(
    file: Buffer | Uint8Array | File,
    originalFilename: string,
    options: MediaUploadOptions
  ): Promise<UploadResult> {
    try {
      const key = this.generateFileKey(options, originalFilename);
      let buffer: Buffer;
      let size: number;
      let contentType: string;

      // Handle different file types
      if (file instanceof File) {
        buffer = Buffer.from(await file.arrayBuffer());
        size = file.size;
        contentType = file.type || options.contentType || 'application/octet-stream';
      } else {
        buffer = Buffer.from(file);
        size = buffer.length;
        contentType = options.contentType || this.getMimeTypeFromFilename(originalFilename);
      }

      // Use Upload for better handling of large files
      const upload = new Upload({
        client: this.client,
        params: {
          Bucket: this.config.bucketName,
          Key: key,
          Body: buffer,
          ContentType: contentType,
          Metadata: {
            userId: options.userId,
            category: options.category || 'general',
            originalFilename: originalFilename,
            uploadedAt: new Date().toISOString(),
          },
        },
      });

      const result = await upload.done();
      
      // Generate public URL
      const url = this.getPublicUrl(key);

      return {
        key,
        url,
        size,
        contentType,
      };
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from S3-compatible storage
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      await this.client.send(command);
    } catch (error) {
      console.error('S3 delete error:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a file exists in S3-compatible storage
   */
  async fileExists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      await this.client.send(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(key: string) {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      const response = await this.client.send(command);
      return {
        size: response.ContentLength,
        contentType: response.ContentType,
        lastModified: response.LastModified,
        metadata: response.Metadata,
      };
    } catch (error) {
      console.error('S3 metadata error:', error);
      throw new Error(`Failed to get file metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate public URL for a file (using signed URLs for security)
   */
  async getPublicUrl(key: string): Promise<string> {
    try {
      // If a public URL is configured, use it (for CDN or public buckets)
      if (this.config.publicUrl) {
        return `${this.config.publicUrl}/${key}`;
      }

      // Generate a signed URL that expires in 24 hours
      const command = new GetObjectCommand({
        Bucket: this.config.bucketName,
        Key: key,
      });

      const signedUrl = await getSignedUrl(this.client, command, {
        expiresIn: 24 * 60 * 60 // 24 hours
      });

      return signedUrl;
    } catch (error) {
      console.error('Error generating signed URL:', error);
      // Fallback to direct URL (might not work if bucket is private)
      if (this.config.endpoint) {
        return `${this.config.endpoint}/${this.config.bucketName}/${key}`;
      }
      return `https://${this.config.bucketName}.s3.${this.config.region}.amazonaws.com/${key}`;
    }
  }

  /**
   * Generate public URL for a file (synchronous version for backward compatibility)
   */
  getPublicUrlSync(key: string): string {
    if (this.config.publicUrl) {
      return `${this.config.publicUrl}/${key}`;
    }

    // Default S3 URL format
    if (this.config.endpoint) {
      return `${this.config.endpoint}/${this.config.bucketName}/${key}`;
    }

    return `https://${this.config.bucketName}.s3.${this.config.region}.amazonaws.com/${key}`;
  }

  /**
   * Get MIME type from filename extension
   */
  private getMimeTypeFromFilename(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    const mimeTypes: Record<string, string> = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      
      // Documents
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'txt': 'text/plain',
      
      // Other
      'json': 'application/json',
      'csv': 'text/csv',
    };

    return mimeTypes[extension || ''] || 'application/octet-stream';
  }
}

// Singleton instance
let s3Storage: S3Storage | null = null;

/**
 * Get or create S3Storage instance
 */
export function getS3Storage(): S3Storage {
  if (!s3Storage) {
    const config: S3Config = {
      endpoint: process.env.S3_ENDPOINT || undefined,
      region: process.env.S3_REGION || 'us-east-1',
      bucketName: process.env.S3_BUCKET_NAME || '',
      accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
      publicUrl: process.env.S3_PUBLIC_URL || undefined,
    };

    // Validate required configuration
    if (!config.bucketName || !config.accessKeyId || !config.secretAccessKey) {
      throw new Error('S3 configuration is incomplete. Please check your environment variables.');
    }

    s3Storage = new S3Storage(config);
  }

  return s3Storage;
}

// Export types and utilities
export type { UploadResult, MediaUploadOptions, S3Config };
export { S3Storage };